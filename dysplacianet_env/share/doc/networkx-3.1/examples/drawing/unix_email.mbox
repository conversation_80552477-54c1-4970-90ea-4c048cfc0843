From alice@edu  Thu Jun 16 16:12:12 2005
From: <PERSON> <alice@edu>
Subject: NetworkX
Date: Thu, 16 Jun 2005 16:12:13 -0700
To: Bob <bob@gov>
Status: RO
Content-Length: 86
Lines: 5

Bob, check out the new networkx release - you and
<PERSON> might really like it.

Alice


From bob@gov   Thu Jun 16 18:13:12 2005
Return-Path: <bob@gov>
Subject: Re: NetworkX
From: Bob <bob@gov>
To: Alice <alice@edu>
Content-Type: text/plain
Date: Thu, 16 Jun 2005 18:13:12 -0700
Status: RO
Content-Length: 26
Lines: 4

Thanks for the tip.

Bob


From ted@com  Thu Jul 28 09:53:31 2005
Return-Path: <ted@com>
Subject: Graph package in Python?
From: Ted <ted@com>
To: Bob <bob@gov>
Content-Type: text/plain
Date: Thu, 28 Jul 2005 09:47:03 -0700
Status: RO
Content-Length: 90
Lines: 3

Hey Ted - I'm looking for a Python package for
graphs and networks.  Do you know of any?


From bob@gov  Thu Jul 28 09:59:31 2005
Return-Path: <bob@gov>
Subject: Re: Graph package in Python?
From: Bob <bob@gov>
To: <PERSON> <ted@com>
Content-Type: text/plain
Date: Thu, 28 Jul 2005 09:59:03 -0700
Status: RO
Content-Length: 180
Lines: 9


Check out the NetworkX package - Alice sent me the tip!

Bob

>> bob@gov scrawled:
>> Hey Ted - I'm looking for a Python package for
>> graphs and networks.  Do you know of any?


From ted@com  Thu Jul 28 15:53:31 2005
Return-Path: <ted@com>
Subject: get together for lunch to discuss Networks?
From: Ted <ted@com>
To: Bob <bob@gov>, Carol <carol@gov>, Alice <alice@edu>
Content-Type: text/plain
Date: Thu, 28 Jul 2005 15:47:03 -0700
Status: RO
Content-Length: 139
Lines: 5

Hey everyrone!  Want to meet at that restaurant on the
island in Konigsburg tonight?  Bring your laptops
and we can install NetworkX.

Ted

