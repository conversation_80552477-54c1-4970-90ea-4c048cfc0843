#!/usr/bin/env python3
"""
Environment Setup Script for DysplaciaNet

This script creates a virtual environment and installs all necessary dependencies
for running DysplaciaNet model with TensorFlow.

Usage:
    python setup_environment.py

Author: Generated for DysplaciaNet Project
"""

import os
import sys
import subprocess
import platform

def run_command(command, description=""):
    """
    Run a shell command and handle errors.
    
    Args:
        command (list): Command to run as list of strings
        description (str): Description of what the command does
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print(f"\n{'='*50}")
        if description:
            print(f"STEP: {description}")
        print(f"Running: {' '.join(command)}")
        print('='*50)
        
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        
        if result.stdout:
            print("Output:")
            print(result.stdout)
        
        print(f"✓ {description if description else 'Command'} completed successfully!")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"✗ Error in {description if description else 'command'}:")
        print(f"Return code: {e.returncode}")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False
    except Exception as e:
        print(f"✗ Unexpected error: {e}")
        return False

def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    print(f"Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 6):
        print("✗ Python 3.6 or higher is required!")
        return False
    
    print("✓ Python version is compatible")
    return True

def create_virtual_environment():
    """Create a virtual environment for the project."""
    venv_name = "dysplacianet_env"
    
    print(f"\nCreating virtual environment: {venv_name}")
    
    # Check if virtual environment already exists
    if os.path.exists(venv_name):
        response = input(f"Virtual environment '{venv_name}' already exists. Recreate it? (y/n): ")
        if response.lower() == 'y':
            print(f"Removing existing virtual environment...")
            if platform.system() == "Windows":
                run_command(["rmdir", "/s", "/q", venv_name], "Removing existing venv")
            else:
                run_command(["rm", "-rf", venv_name], "Removing existing venv")
        else:
            print("Using existing virtual environment.")
            return True
    
    # Create new virtual environment
    success = run_command([sys.executable, "-m", "venv", venv_name], 
                         "Creating virtual environment")
    
    if success:
        print(f"\n✓ Virtual environment '{venv_name}' created successfully!")
        print(f"\nTo activate the environment:")
        if platform.system() == "Windows":
            print(f"  {venv_name}\\Scripts\\activate")
        else:
            print(f"  source {venv_name}/bin/activate")
    
    return success

def install_dependencies():
    """Install dependencies in the virtual environment."""
    venv_name = "dysplacianet_env"
    
    # Determine pip path based on OS
    if platform.system() == "Windows":
        pip_path = os.path.join(venv_name, "Scripts", "pip")
        python_path = os.path.join(venv_name, "Scripts", "python")
    else:
        pip_path = os.path.join(venv_name, "bin", "pip")
        python_path = os.path.join(venv_name, "bin", "python")
    
    # Upgrade pip first
    success = run_command([python_path, "-m", "pip", "install", "--upgrade", "pip"], 
                         "Upgrading pip")
    if not success:
        return False
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        success = run_command([pip_path, "install", "-r", "requirements.txt"], 
                             "Installing requirements from requirements.txt")
        if not success:
            return False
    else:
        print("requirements.txt not found. Installing basic dependencies...")
        
        # Install basic dependencies manually
        dependencies = [
            "tensorflow==2.3.0",
            "keras==2.4.3",
            "pillow",
            "matplotlib==3.3.4",
            "keras-vis",
            "scipy==1.6.2",
            "numpy==1.19.2",
            "opencv-python==********",
            "scikit-learn>=0.24.0",
            "seaborn>=0.11.0",
            "pandas>=1.2.0"
        ]
        
        for dep in dependencies:
            success = run_command([pip_path, "install", dep], 
                                 f"Installing {dep}")
            if not success:
                print(f"Failed to install {dep}, continuing with others...")
    
    return True

def verify_installation():
    """Verify that key packages are installed correctly."""
    venv_name = "dysplacianet_env"
    
    if platform.system() == "Windows":
        python_path = os.path.join(venv_name, "Scripts", "python")
    else:
        python_path = os.path.join(venv_name, "bin", "python")
    
    test_script = '''
import sys
print("Python version:", sys.version)

try:
    import tensorflow as tf
    print("✓ TensorFlow version:", tf.__version__)
except ImportError as e:
    print("✗ TensorFlow import failed:", e)

try:
    import keras
    print("✓ Keras version:", keras.__version__)
except ImportError as e:
    print("✗ Keras import failed:", e)

try:
    import numpy as np
    print("✓ NumPy version:", np.__version__)
except ImportError as e:
    print("✗ NumPy import failed:", e)

try:
    import matplotlib
    print("✓ Matplotlib version:", matplotlib.__version__)
except ImportError as e:
    print("✗ Matplotlib import failed:", e)

try:
    import sklearn
    print("✓ Scikit-learn version:", sklearn.__version__)
except ImportError as e:
    print("✗ Scikit-learn import failed:", e)

try:
    import cv2
    print("✓ OpenCV version:", cv2.__version__)
except ImportError as e:
    print("✗ OpenCV import failed:", e)
'''
    
    success = run_command([python_path, "-c", test_script], 
                         "Verifying package installations")
    return success

def main():
    """Main setup function."""
    print("DysplaciaNet Environment Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create virtual environment
    if not create_virtual_environment():
        print("✗ Failed to create virtual environment")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("✗ Failed to install dependencies")
        sys.exit(1)
    
    # Verify installation
    print("\n" + "="*50)
    print("VERIFYING INSTALLATION")
    print("="*50)
    
    if verify_installation():
        print("\n" + "="*50)
        print("✓ SETUP COMPLETED SUCCESSFULLY!")
        print("="*50)
        print("\nNext steps:")
        print("1. Activate the virtual environment:")
        if platform.system() == "Windows":
            print("   dysplacianet_env\\Scripts\\activate")
        else:
            print("   source dysplacianet_env/bin/activate")
        print("2. Run the model loader:")
        print("   python model_loader.py")
        print("3. Run the dataset visualizer:")
        print("   python dataset_visualizer.py")
    else:
        print("\n✗ Setup completed with some issues. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
