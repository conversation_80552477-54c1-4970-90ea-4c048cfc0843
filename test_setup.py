#!/usr/bin/env python3
"""
Test Script for DysplaciaNet Setup

This script tests that all components are working correctly:
- Virtual environment
- Dependencies
- Model loading
- Visualization functions

Usage:
    source dysplacianet_env/bin/activate
    python test_setup.py

Author: Generated for DysplaciaNet Project
"""

import sys
import os
import traceback

def test_imports():
    """Test that all required packages can be imported."""
    print("Testing package imports...")
    
    try:
        import tensorflow as tf
        print(f"✓ TensorFlow {tf.__version__}")
    except ImportError as e:
        print(f"✗ TensorFlow import failed: {e}")
        return False
    
    try:
        import keras
        print(f"✓ Keras {keras.__version__}")
    except ImportError as e:
        print(f"✗ Keras import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy {np.__version__}")
    except ImportError as e:
        print(f"✗ NumPy import failed: {e}")
        return False
    
    try:
        import matplotlib
        print(f"✓ Matplotlib {matplotlib.__version__}")
    except ImportError as e:
        print(f"✗ Matplotlib import failed: {e}")
        return False
    
    try:
        import sklearn
        print(f"✓ Scikit-learn {sklearn.__version__}")
    except ImportError as e:
        print(f"✗ Scikit-learn import failed: {e}")
        return False
    
    try:
        import cv2
        print(f"✓ OpenCV {cv2.__version__}")
    except ImportError as e:
        print(f"✗ OpenCV import failed: {e}")
        return False
    
    try:
        import seaborn as sns
        print(f"✓ Seaborn {sns.__version__}")
    except ImportError as e:
        print(f"✗ Seaborn import failed: {e}")
        return False
    
    try:
        import pandas as pd
        print(f"✓ Pandas {pd.__version__}")
    except ImportError as e:
        print(f"✗ Pandas import failed: {e}")
        return False
    
    return True

def test_model_files():
    """Test that model files exist."""
    print("\nTesting model files...")
    
    model_json = "model/final_model.json"
    model_weights = "model/final_model_weights.h5"
    
    if os.path.exists(model_json):
        print(f"✓ Model JSON found: {model_json}")
    else:
        print(f"✗ Model JSON not found: {model_json}")
        return False
    
    if os.path.exists(model_weights):
        print(f"✓ Model weights found: {model_weights}")
    else:
        print(f"✗ Model weights not found: {model_weights}")
        return False
    
    return True

def test_model_loader():
    """Test the model loader functionality."""
    print("\nTesting model loader...")
    
    try:
        from model_loader import DysplaciaNetLoader
        print("✓ Model loader imported successfully")
        
        # Initialize loader
        loader = DysplaciaNetLoader()
        print("✓ Model loader initialized")
        
        # Load model
        if loader.load_model():
            print("✓ Model loaded successfully")
        else:
            print("✗ Model loading failed")
            return False
        
        # Test model info
        metrics = loader.get_model_metrics()
        if metrics:
            print("✓ Model metrics extracted")
            print(f"  - Total parameters: {metrics['model_info']['total_parameters']:,}")
            print(f"  - Input shape: {metrics['model_info']['input_shape']}")
            print(f"  - Output shape: {metrics['model_info']['output_shape']}")
        else:
            print("✗ Failed to extract model metrics")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Model loader test failed: {e}")
        traceback.print_exc()
        return False

def test_dataset_visualizer():
    """Test the dataset visualizer functionality."""
    print("\nTesting dataset visualizer...")
    
    try:
        from dataset_visualizer import DatasetVisualizer
        print("✓ Dataset visualizer imported successfully")
        
        # Initialize visualizer
        viz = DatasetVisualizer()
        print("✓ Dataset visualizer initialized")
        
        # Test with synthetic data (no plots displayed in test)
        import matplotlib
        matplotlib.use('Agg')  # Use non-interactive backend for testing
        
        # Test class distribution
        labels = [0, 1, 0, 1, 0, 1]
        viz.plot_class_distribution(labels, title="Test Distribution")
        print("✓ Class distribution plot test passed")
        
        # Test prediction analysis
        true_labels = [0, 1, 0, 1]
        predictions = [0, 1, 1, 1]
        probabilities = [0.2, 0.8, 0.6, 0.9]
        viz.plot_prediction_analysis(true_labels, predictions, probabilities)
        print("✓ Prediction analysis plot test passed")
        
        return True
        
    except Exception as e:
        print(f"✗ Dataset visualizer test failed: {e}")
        traceback.print_exc()
        return False

def test_prediction():
    """Test making a prediction with synthetic data."""
    print("\nTesting prediction functionality...")
    
    try:
        from model_loader import DysplaciaNetLoader
        from PIL import Image
        import numpy as np
        
        # Load model
        loader = DysplaciaNetLoader()
        if not loader.load_model():
            print("✗ Could not load model for prediction test")
            return False
        
        # Create synthetic test image
        test_img = np.random.rand(299, 299, 3) * 255
        test_img_path = 'test_prediction_image.jpg'
        Image.fromarray(test_img.astype(np.uint8)).save(test_img_path)
        
        # Make prediction
        result = loader.predict_single_image(test_img_path, verbose=False)
        
        if result:
            print("✓ Prediction test successful")
            print(f"  - Predicted class: {result['predicted_class_name']}")
            print(f"  - Confidence: {result['confidence']:.3f}")
            print(f"  - Raw probability: {result['raw_probability']:.6f}")
        else:
            print("✗ Prediction test failed")
            return False
        
        # Clean up
        os.remove(test_img_path)
        print("✓ Test image cleaned up")
        
        return True
        
    except Exception as e:
        print(f"✗ Prediction test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("DysplaciaNet Setup Test Suite")
    print("=" * 40)
    
    tests = [
        ("Package Imports", test_imports),
        ("Model Files", test_model_files),
        ("Model Loader", test_model_loader),
        ("Dataset Visualizer", test_dataset_visualizer),
        ("Prediction", test_prediction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"RUNNING: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Setup is working correctly.")
        print("\nYou can now use:")
        print("  - python model_loader.py")
        print("  - python dataset_visualizer.py")
        print("  - from model_loader import DysplaciaNetLoader")
        print("  - from dataset_visualizer import DatasetVisualizer")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
