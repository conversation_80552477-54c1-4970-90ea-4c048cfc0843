#!/usr/bin/env python3
"""
DysplaciaNet Model Loader and Metrics Calculator

This script loads the DysplaciaNet model and provides comprehensive metrics
and evaluation capabilities.

Usage:
    python model_loader.py

Author: Generated for DysplaciaNet Project
"""

import os
import sys
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# TensorFlow and Keras imports
try:
    import tensorflow as tf
    import keras
    from keras.models import model_from_json
    from keras.preprocessing import image
    print(f"✓ TensorFlow version: {tf.__version__}")
    print(f"✓ Keras version: {keras.__version__}")
except ImportError as e:
    print(f"✗ Error importing TensorFlow/Keras: {e}")
    print("Please make sure you have activated the virtual environment and installed dependencies.")
    sys.exit(1)

# Scikit-learn imports
try:
    from sklearn.metrics import (
        accuracy_score, precision_score, recall_score, f1_score,
        confusion_matrix, classification_report, roc_auc_score,
        roc_curve, precision_recall_curve, average_precision_score
    )
    print("✓ Scikit-learn imported successfully")
except ImportError as e:
    print(f"✗ Error importing scikit-learn: {e}")
    sys.exit(1)

class DysplaciaNetLoader:
    """
    Comprehensive DysplaciaNet model loader with advanced metrics calculation.
    """
    
    def __init__(self, model_path='model/final_model.json', weights_path='model/final_model_weights.h5'):
        """
        Initialize the model loader.
        
        Args:
            model_path (str): Path to model JSON file
            weights_path (str): Path to model weights file
        """
        self.model_path = model_path
        self.weights_path = weights_path
        self.model = None
        self.img_height = 299
        self.img_width = 299
        self.model_info = {}
        
        print("DysplaciaNet Model Loader Initialized")
        print(f"Model JSON: {model_path}")
        print(f"Model Weights: {weights_path}")
    
    def load_model(self):
        """
        Load the DysplaciaNet model from JSON and weights files.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print("\n" + "="*50)
            print("LOADING DYSPLACIANET MODEL")
            print("="*50)
            
            # Check if files exist
            if not os.path.exists(self.model_path):
                print(f"✗ Model JSON file not found: {self.model_path}")
                return False
            
            if not os.path.exists(self.weights_path):
                print(f"✗ Model weights file not found: {self.weights_path}")
                return False
            
            print(f"✓ Model files found")
            
            # Load model architecture from JSON
            print("Loading model architecture...")
            with open(self.model_path, 'r') as json_file:
                json_config = json_file.read()
            
            self.model = model_from_json(json_config)
            print("✓ Model architecture loaded")
            
            # Load weights
            print("Loading model weights...")
            self.model.load_weights(self.weights_path)
            print("✓ Model weights loaded")
            
            # Compile model
            print("Compiling model...")
            self.model.compile(
                loss='binary_crossentropy',
                optimizer='adam',
                metrics=['accuracy', 'precision', 'recall']
            )
            print("✓ Model compiled")
            
            # Store model information
            self._extract_model_info()
            
            print("\n✓ MODEL LOADED SUCCESSFULLY!")
            return True
            
        except Exception as e:
            print(f"✗ Error loading model: {e}")
            return False
    
    def _extract_model_info(self):
        """Extract and store model information."""
        if self.model is None:
            return
        
        self.model_info = {
            'input_shape': self.model.input_shape,
            'output_shape': self.model.output_shape,
            'total_parameters': self.model.count_params(),
            'trainable_parameters': sum([tf.keras.backend.count_params(w) for w in self.model.trainable_weights]),
            'non_trainable_parameters': sum([tf.keras.backend.count_params(w) for w in self.model.non_trainable_weights]),
            'num_layers': len(self.model.layers),
            'layer_names': [layer.name for layer in self.model.layers],
            'layer_types': [type(layer).__name__ for layer in self.model.layers]
        }
    
    def print_model_summary(self):
        """Print comprehensive model summary."""
        if self.model is None:
            print("✗ Model not loaded. Please call load_model() first.")
            return
        
        print("\n" + "="*60)
        print("DYSPLACIANET MODEL SUMMARY")
        print("="*60)
        
        print(f"Input Shape: {self.model_info['input_shape']}")
        print(f"Output Shape: {self.model_info['output_shape']}")
        print(f"Total Parameters: {self.model_info['total_parameters']:,}")
        print(f"Trainable Parameters: {self.model_info['trainable_parameters']:,}")
        print(f"Non-trainable Parameters: {self.model_info['non_trainable_parameters']:,}")
        print(f"Number of Layers: {self.model_info['num_layers']}")
        
        print(f"\nModel Architecture:")
        print(f"- Input: {self.img_height}x{self.img_width}x3 RGB images")
        print(f"- Task: Binary classification (Dysplastic vs Normal)")
        print(f"- Output: Single probability value (sigmoid activation)")
        
        print(f"\nLayer Details:")
        print("-" * 60)
        for i, (name, layer_type) in enumerate(zip(self.model_info['layer_names'], 
                                                  self.model_info['layer_types'])):
            layer = self.model.layers[i]
            output_shape = str(layer.output_shape) if hasattr(layer, 'output_shape') else 'N/A'
            params = layer.count_params() if hasattr(layer, 'count_params') else 0
            print(f"{i+1:3d}. {name:<20} {layer_type:<15} {output_shape:<25} {params:>8,} params")
        
        print("\n" + "="*60)
        print("KERAS MODEL SUMMARY")
        print("="*60)
        self.model.summary()
    
    def get_model_metrics(self):
        """
        Get comprehensive model metrics and information.
        
        Returns:
            dict: Dictionary containing all model metrics and information
        """
        if self.model is None:
            print("✗ Model not loaded. Please call load_model() first.")
            return None
        
        metrics = {
            'model_info': self.model_info.copy(),
            'architecture_details': {
                'model_type': 'Convolutional Neural Network',
                'task': 'Binary Classification',
                'classes': ['Dysplastic', 'Normal'],
                'input_preprocessing': 'Normalization to [0,1]',
                'loss_function': 'Binary Crossentropy',
                'optimizer': 'Adam',
                'activation_output': 'Sigmoid'
            },
            'computational_complexity': {
                'input_size': self.img_height * self.img_width * 3,
                'memory_usage_mb': self._estimate_memory_usage(),
                'flops_estimate': self._estimate_flops()
            }
        }
        
        return metrics
    
    def _estimate_memory_usage(self):
        """Estimate model memory usage in MB."""
        if self.model is None:
            return 0
        
        # Rough estimation based on parameters and typical activation sizes
        param_memory = self.model_info['total_parameters'] * 4 / (1024 * 1024)  # 4 bytes per float32
        activation_memory = 50  # Rough estimate for activations
        return param_memory + activation_memory
    
    def _estimate_flops(self):
        """Estimate FLOPs (Floating Point Operations) for the model."""
        # This is a rough estimation - for precise FLOP counting, 
        # you would need specialized tools
        return self.model_info['total_parameters'] * 2  # Rough estimate
    
    def predict_single_image(self, image_path, verbose=True):
        """
        Make prediction on a single image with detailed output.
        
        Args:
            image_path (str): Path to image file
            verbose (bool): Whether to print detailed results
            
        Returns:
            dict: Detailed prediction results
        """
        if self.model is None:
            print("✗ Model not loaded. Please call load_model() first.")
            return None
        
        try:
            # Load and preprocess image
            img = image.load_img(image_path, target_size=(self.img_height, self.img_width))
            img_array = image.img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array = img_array / 255.0  # Normalize to [0,1]
            
            # Make prediction
            prediction_prob = self.model.predict(img_array, verbose=0)[0][0]
            predicted_class = 1 if prediction_prob > 0.5 else 0
            
            # Calculate confidence and class names
            class_name = "Normal" if predicted_class == 1 else "Dysplastic"
            confidence = prediction_prob if predicted_class == 1 else (1 - prediction_prob)
            
            results = {
                'image_path': image_path,
                'image_name': os.path.basename(image_path),
                'raw_probability': float(prediction_prob),
                'predicted_class': int(predicted_class),
                'predicted_class_name': class_name,
                'confidence': float(confidence),
                'threshold': 0.5,
                'preprocessing': 'Resized to 299x299, normalized to [0,1]'
            }
            
            if verbose:
                print(f"\nPrediction Results for: {results['image_name']}")
                print(f"  Predicted Class: {results['predicted_class_name']}")
                print(f"  Confidence: {results['confidence']:.3f}")
                print(f"  Raw Probability: {results['raw_probability']:.6f}")
                print(f"  Threshold: {results['threshold']}")
            
            return results
            
        except Exception as e:
            print(f"✗ Error processing image {image_path}: {e}")
            return None
    
    def save_model_info(self, output_file='model_info.json'):
        """
        Save model information to JSON file.
        
        Args:
            output_file (str): Output file path
        """
        if self.model is None:
            print("✗ Model not loaded. Please call load_model() first.")
            return
        
        metrics = self.get_model_metrics()
        
        try:
            with open(output_file, 'w') as f:
                json.dump(metrics, f, indent=2, default=str)
            print(f"✓ Model information saved to: {output_file}")
        except Exception as e:
            print(f"✗ Error saving model info: {e}")


def main():
    """Main function to demonstrate model loading and metrics."""
    print("DysplaciaNet Model Loader and Metrics Calculator")
    print("=" * 55)
    
    # Initialize loader
    loader = DysplaciaNetLoader()
    
    # Load model
    if not loader.load_model():
        print("✗ Failed to load model. Please check the model files.")
        sys.exit(1)
    
    # Print model summary
    loader.print_model_summary()
    
    # Get and display metrics
    print("\n" + "="*50)
    print("EXTRACTING MODEL METRICS")
    print("="*50)
    
    metrics = loader.get_model_metrics()
    if metrics:
        print("✓ Model metrics extracted successfully")
        
        # Save metrics to file
        loader.save_model_info('dysplacianet_model_info.json')
        
        print(f"\nComputational Complexity:")
        comp = metrics['computational_complexity']
        print(f"  Input Size: {comp['input_size']:,} pixels")
        print(f"  Estimated Memory Usage: {comp['memory_usage_mb']:.1f} MB")
        print(f"  Estimated FLOPs: {comp['flops_estimate']:,}")
    
    # Test with a sample image if available
    print("\n" + "="*50)
    print("TESTING PREDICTION CAPABILITY")
    print("="*50)
    
    # Create a synthetic test image for demonstration
    print("Creating synthetic test image...")
    test_img = np.random.rand(299, 299, 3) * 255
    test_img_path = 'test_synthetic_image.jpg'
    
    try:
        from PIL import Image
        Image.fromarray(test_img.astype(np.uint8)).save(test_img_path)
        print(f"✓ Synthetic test image created: {test_img_path}")
        
        # Test prediction
        result = loader.predict_single_image(test_img_path)
        if result:
            print("✓ Prediction test successful")
        
        # Clean up
        os.remove(test_img_path)
        print("✓ Test image cleaned up")
        
    except Exception as e:
        print(f"✗ Error in prediction test: {e}")
    
    print("\n" + "="*50)
    print("MODEL LOADING COMPLETE!")
    print("="*50)
    print("The model is now ready for use.")
    print("You can use the following methods:")
    print("  - loader.predict_single_image('path/to/image.jpg')")
    print("  - loader.get_model_metrics()")
    print("  - loader.print_model_summary()")


if __name__ == "__main__":
    main()
