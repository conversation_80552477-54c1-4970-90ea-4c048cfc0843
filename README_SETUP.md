# DysplaciaNet Environment Setup and Usage Guide

This guide provides comprehensive instructions for setting up the TensorFlow environment and using the DysplaciaNet model with all necessary tools for model loading, metrics calculation, and dataset visualization.

## 📋 Overview

This project includes:
- **Virtual Environment Setup**: Automated creation of TensorFlow environment
- **Model Loader**: Comprehensive model loading with detailed metrics
- **Dataset Visualizer**: Advanced visualization functions for dataset analysis
- **Pre-trained Model**: DysplaciaNet weights and architecture files

## 🚀 Quick Start

### 1. Environment Setup

Run the automated setup script to create the virtual environment and install all dependencies:

```bash
python setup_environment.py
```

This script will:
- Create a virtual environment named `dysplacianet_env`
- Install TensorFlow 2.3.0 and all required dependencies
- Verify the installation
- Provide activation instructions

### 2. Manual Environment Setup (Alternative)

If the automated setup fails, you can set up manually:

```bash
# Create virtual environment
python3 -m virtualenv dysplacianet_env

# Activate environment
source dysplacianet_env/bin/activate  # Linux/Mac
# OR
dysplacianet_env\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt

# Fix protobuf compatibility if needed
pip install protobuf==3.20.3
```

### 3. Activate Environment

Before running any scripts, always activate the virtual environment:

```bash
source dysplacianet_env/bin/activate
```

## 📊 Model Loading and Metrics

### Load Model and Get Comprehensive Metrics

```bash
python model_loader.py
```

This script provides:
- **Model Architecture Analysis**: Detailed layer information
- **Parameter Counting**: Total, trainable, and non-trainable parameters
- **Computational Complexity**: Memory usage and FLOP estimates
- **Model Summary**: Complete Keras model summary
- **Prediction Testing**: Synthetic image prediction test
- **JSON Export**: Model information saved to `dysplacianet_model_info.json`

### Model Information Output

The model loader provides:

```
✓ TensorFlow version: 2.3.4
✓ Keras version: 2.4.3
✓ Model loaded successfully

Model Architecture:
- Input: 299x299x3 RGB images
- Task: Binary classification (Dysplastic vs Normal)
- Output: Single probability value (sigmoid activation)
- Total Parameters: 72,977
- Layers: 18 (4 Conv2D, 4 MaxPooling2D, 2 Dense, etc.)
```

### Using Model Loader Programmatically

```python
from model_loader import DysplaciaNetLoader

# Initialize loader
loader = DysplaciaNetLoader()

# Load model
if loader.load_model():
    # Get model metrics
    metrics = loader.get_model_metrics()
    
    # Make prediction on image
    result = loader.predict_single_image('path/to/image.jpg')
    
    # Print model summary
    loader.print_model_summary()
```

## 🎨 Dataset Visualization

### Run Visualization Demo

```bash
python dataset_visualizer.py
```

This script demonstrates:
- **Image Grid Display**: Multiple images with labels and predictions
- **Class Distribution**: Bar charts and pie charts
- **Prediction Analysis**: Confusion matrix, ROC curves, metrics
- **Synthetic Data Demo**: Example with generated test data

### Using Visualizer Programmatically

```python
from dataset_visualizer import DatasetVisualizer

# Initialize visualizer
viz = DatasetVisualizer()

# Display image grid
viz.visualize_image_grid(
    image_paths=['img1.jpg', 'img2.jpg'],
    labels=[0, 1],  # 0=dysplastic, 1=normal
    predictions=[0, 1],
    grid_size=(2, 1)
)

# Plot class distribution
viz.plot_class_distribution([0, 0, 1, 1, 0], title="Dataset Distribution")

# Comprehensive prediction analysis
viz.plot_prediction_analysis(
    true_labels=[0, 1, 0, 1],
    predictions=[0, 1, 1, 1],
    probabilities=[0.2, 0.8, 0.6, 0.9]
)
```

## 📁 Project Structure

```
dysplacianet/
├── dysplacianet_env/           # Virtual environment
├── model/
│   ├── final_model.json        # Model architecture
│   └── final_model_weights.h5  # Pre-trained weights
├── setup_environment.py        # Environment setup script
├── model_loader.py             # Model loading and metrics
├── dataset_visualizer.py       # Visualization functions
├── requirements.txt            # Python dependencies
├── dysplacianet_model_info.json # Generated model info
└── README_SETUP.md             # This file
```

## 🔧 Dependencies

### Core Dependencies
- **TensorFlow**: 2.3.0 (Deep learning framework)
- **Keras**: 2.4.3 (High-level neural networks API)
- **NumPy**: 1.18.5 (Numerical computing)
- **OpenCV**: ******** (Computer vision)

### Visualization Dependencies
- **Matplotlib**: 3.3.4 (Plotting library)
- **Seaborn**: 0.12.2 (Statistical visualization)
- **Pillow**: Latest (Image processing)

### Analysis Dependencies
- **Scikit-learn**: 1.3.2 (Machine learning metrics)
- **Pandas**: 1.4.4 (Data manipulation)
- **SciPy**: 1.6.3 (Scientific computing)

## 🎯 Model Details

### DysplaciaNet Architecture
- **Input**: 299×299×3 RGB images
- **Task**: Binary classification (Dysplastic vs Normal cells)
- **Architecture**: Convolutional Neural Network
- **Layers**: 18 total (4 Conv2D + pooling, 2 Dense)
- **Parameters**: 72,977 total
- **Output**: Single probability (sigmoid activation)

### Model Performance Metrics Available
- Accuracy, Precision, Recall, F1-Score
- Confusion Matrix
- ROC Curve and AUC
- Precision-Recall Curve
- Class-wise accuracy
- Prediction probability distributions

## 🔍 Usage Examples

### Example 1: Load Model and Analyze

```python
from model_loader import DysplaciaNetLoader

loader = DysplaciaNetLoader()
loader.load_model()
loader.print_model_summary()

# Get detailed metrics
metrics = loader.get_model_metrics()
print(f"Total parameters: {metrics['model_info']['total_parameters']:,}")
print(f"Memory usage: {metrics['computational_complexity']['memory_usage_mb']:.1f} MB")
```

### Example 2: Predict on Image

```python
# Single image prediction
result = loader.predict_single_image('cell_image.jpg')
print(f"Prediction: {result['predicted_class_name']}")
print(f"Confidence: {result['confidence']:.3f}")
```

### Example 3: Visualize Dataset

```python
from dataset_visualizer import DatasetVisualizer

viz = DatasetVisualizer()

# Create comprehensive analysis
image_paths = ['img1.jpg', 'img2.jpg', 'img3.jpg']
true_labels = [0, 1, 0]  # 0=dysplastic, 1=normal
predictions = [0, 1, 1]
probabilities = [0.2, 0.8, 0.6]

viz.plot_prediction_analysis(true_labels, predictions, probabilities)
```

## 🚨 Troubleshooting

### Common Issues

1. **Protobuf Error**: If you get protobuf descriptor errors:
   ```bash
   pip install protobuf==3.20.3
   ```

2. **CUDA Warnings**: GPU warnings are normal if no GPU is available. The model runs on CPU.

3. **Virtual Environment Issues**: If venv creation fails:
   ```bash
   pip install --user virtualenv
   python3 -m virtualenv dysplacianet_env
   ```

4. **Import Errors**: Always activate the virtual environment:
   ```bash
   source dysplacianet_env/bin/activate
   ```

## 📈 Next Steps

1. **Load your own images**: Use `loader.predict_single_image('your_image.jpg')`
2. **Evaluate on dataset**: Use the existing evaluation scripts with your data
3. **Customize visualizations**: Modify the visualizer for your specific needs
4. **Export results**: Model info is automatically saved to JSON

## 🤝 Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all dependencies are installed correctly
3. Ensure the virtual environment is activated
4. Check that model files exist in the `model/` directory

---

**Environment Status**: ✅ Ready for use
**Model Status**: ✅ Loaded and tested
**Visualizations**: ✅ Working with demo data
