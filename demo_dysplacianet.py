#!/usr/bin/env python3
"""
DysplaciaNet Demo Script

A simplified demo script that loads the DysplaciaNet model and demonstrates
its capabilities with or without actual image data.

Author: Generated for DysplaciaNet Interpretability Study
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import keras
from keras.preprocessing import image
import json
from pathlib import Path

class DysplaciaNetDemo:
    """
    A simplified demo class for DysplaciaNet model.
    """
    
    def __init__(self):
        """Initialize the demo."""
        self.model = None
        self.img_height = 299
        self.img_width = 299
        
    def load_model(self, model_path='model/final_model.json', weights_path='model/final_model_weights.h5'):
        """
        Load the DysplaciaNet model.
        
        Args:
            model_path (str): Path to model JSON file
            weights_path (str): Path to model weights file
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            print("Loading DysplaciaNet model...")
            
            # Check if files exist
            if not os.path.exists(model_path):
                print(f"Model file not found: {model_path}")
                return False
            if not os.path.exists(weights_path):
                print(f"Weights file not found: {weights_path}")
                return False
            
            # Load model architecture
            with open(model_path, 'r') as json_file:
                json_config = json_file.read()
            
            self.model = keras.models.model_from_json(json_config)
            
            # Load weights
            self.model.load_weights(weights_path)
            
            # Compile model
            self.model.compile(
                loss='binary_crossentropy',
                optimizer='adam',
                metrics=['accuracy']
            )
            
            print("✓ Model loaded successfully!")
            return True
            
        except Exception as e:
            print(f"✗ Error loading model: {e}")
            return False
    
    def show_model_info(self):
        """Display model information."""
        if self.model is None:
            print("Model not loaded.")
            return
        
        print("\n" + "="*50)
        print("DYSPLACIANET MODEL INFORMATION")
        print("="*50)
        
        print(f"Input shape: {self.model.input_shape}")
        print(f"Output shape: {self.model.output_shape}")
        print(f"Total parameters: {self.model.count_params():,}")
        
        print(f"\nModel Architecture:")
        print(f"- Input: {self.img_height}x{self.img_width}x3 RGB images")
        print(f"- Task: Binary classification (Dysplastic vs Normal cells)")
        print(f"- Output: Single probability value (sigmoid activation)")
        
        print(f"\nLayer Summary:")
        for i, layer in enumerate(self.model.layers):
            print(f"  {i+1:2d}. {layer.name:<15} {str(layer.output_shape):<20}")
    
    def predict_single_image(self, image_path):
        """
        Make prediction on a single image.
        
        Args:
            image_path (str): Path to image file
            
        Returns:
            dict: Prediction results
        """
        if self.model is None:
            print("Model not loaded.")
            return None
        
        try:
            # Load and preprocess image
            img = image.load_img(image_path, target_size=(self.img_height, self.img_width))
            img_array = image.img_to_array(img)
            img_array = np.expand_dims(img_array, axis=0)
            img_array /= 255.0
            
            # Make prediction
            prediction_prob = self.model.predict(img_array, verbose=0)[0][0]
            predicted_class = 1 if prediction_prob > 0.5 else 0
            
            # Prepare results
            class_name = "Normal" if predicted_class == 1 else "Dysplastic"
            confidence = prediction_prob if predicted_class == 1 else (1 - prediction_prob)
            
            results = {
                'image_path': image_path,
                'probability': float(prediction_prob),
                'predicted_class': int(predicted_class),
                'class_name': class_name,
                'confidence': float(confidence)
            }
            
            return results
            
        except Exception as e:
            print(f"Error processing image {image_path}: {e}")
            return None
    
    def demo_with_synthetic_data(self):
        """
        Demonstrate model capabilities with synthetic data.
        """
        if self.model is None:
            print("Model not loaded.")
            return
        
        print("\n" + "="*50)
        print("DEMO WITH SYNTHETIC DATA")
        print("="*50)
        
        # Generate synthetic images
        print("Generating synthetic test images...")
        
        # Create different types of synthetic images
        synthetic_images = []
        image_types = []
        
        # Type 1: Random noise
        random_img = np.random.rand(self.img_height, self.img_width, 3)
        synthetic_images.append(random_img)
        image_types.append("Random Noise")
        
        # Type 2: Gradient pattern
        gradient_img = np.zeros((self.img_height, self.img_width, 3))
        for i in range(self.img_height):
            gradient_img[i, :, :] = i / self.img_height
        synthetic_images.append(gradient_img)
        image_types.append("Gradient Pattern")
        
        # Type 3: Checkerboard pattern
        checker_img = np.zeros((self.img_height, self.img_width, 3))
        checker_size = 20
        for i in range(0, self.img_height, checker_size):
            for j in range(0, self.img_width, checker_size):
                if (i // checker_size + j // checker_size) % 2 == 0:
                    checker_img[i:i+checker_size, j:j+checker_size, :] = 1
        synthetic_images.append(checker_img)
        image_types.append("Checkerboard")
        
        # Type 4: Circular pattern
        circle_img = np.zeros((self.img_height, self.img_width, 3))
        center_x, center_y = self.img_width // 2, self.img_height // 2
        y, x = np.ogrid[:self.img_height, :self.img_width]
        mask = (x - center_x)**2 + (y - center_y)**2 <= (min(self.img_width, self.img_height) // 4)**2
        circle_img[mask] = 1
        synthetic_images.append(circle_img)
        image_types.append("Circle Pattern")
        
        # Make predictions and visualize
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        axes = axes.flatten()
        
        for i, (img, img_type) in enumerate(zip(synthetic_images, image_types)):
            # Prepare image for prediction
            img_batch = np.expand_dims(img, axis=0)
            
            # Make prediction
            prediction_prob = self.model.predict(img_batch, verbose=0)[0][0]
            predicted_class = 1 if prediction_prob > 0.5 else 0
            class_name = "Normal" if predicted_class == 1 else "Dysplastic"
            confidence = prediction_prob if predicted_class == 1 else (1 - prediction_prob)
            
            # Display image
            axes[i].imshow(img)
            axes[i].set_title(f"{img_type}\nPred: {class_name} ({confidence:.3f})", 
                            fontsize=10, weight='bold')
            axes[i].axis('off')
            
            # Print results
            print(f"{i+1}. {img_type:<15} -> {class_name:<10} (confidence: {confidence:.3f})")
        
        plt.suptitle('DysplaciaNet Predictions on Synthetic Images', fontsize=14, weight='bold')
        plt.tight_layout()
        plt.show()
    
    def demo_model_layers(self):
        """
        Demonstrate model layer activations with synthetic input.
        """
        if self.model is None:
            print("Model not loaded.")
            return
        
        print("\n" + "="*50)
        print("MODEL LAYER ANALYSIS")
        print("="*50)
        
        # Create a synthetic input
        synthetic_input = np.random.rand(1, self.img_height, self.img_width, 3)
        
        # Get outputs from different layers
        layer_outputs = []
        layer_names = []
        
        for i, layer in enumerate(self.model.layers):
            if hasattr(layer, 'output_shape') and len(layer.output_shape) > 1:
                try:
                    # Create a model that outputs this layer
                    temp_model = keras.Model(inputs=self.model.input, outputs=layer.output)
                    output = temp_model.predict(synthetic_input, verbose=0)
                    layer_outputs.append(output)
                    layer_names.append(f"{i+1}. {layer.name}")
                except:
                    continue
        
        print(f"Analyzed {len(layer_outputs)} layers:")
        for name, output in zip(layer_names, layer_outputs):
            print(f"  {name:<20} Output shape: {output.shape}")
        
        print(f"\nFinal prediction: {self.model.predict(synthetic_input, verbose=0)[0][0]:.6f}")


def main():
    """Main demo function."""
    print("DysplaciaNet Model Demo")
    print("=" * 30)
    
    # Initialize demo
    demo = DysplaciaNetDemo()
    
    # Load model
    if not demo.load_model():
        print("Failed to load model. Please check that the model files exist.")
        return
    
    # Show model information
    demo.show_model_info()
    
    # Demo with synthetic data
    demo.demo_with_synthetic_data()
    
    # Demo model layers
    demo.demo_model_layers()
    
    # Check if user wants to test with real images
    print("\n" + "="*50)
    print("REAL IMAGE TESTING")
    print("="*50)
    
    image_path = input("Enter path to an image file (or press Enter to skip): ").strip()
    
    if image_path and os.path.exists(image_path):
        print(f"\nTesting with image: {image_path}")
        results = demo.predict_single_image(image_path)
        
        if results:
            print(f"Results:")
            print(f"  Predicted class: {results['class_name']}")
            print(f"  Confidence: {results['confidence']:.3f}")
            print(f"  Raw probability: {results['probability']:.6f}")
            
            # Display the image
            img = image.load_img(image_path, target_size=(demo.img_width, demo.img_height))
            plt.figure(figsize=(8, 6))
            plt.imshow(img)
            plt.title(f"Prediction: {results['class_name']} (confidence: {results['confidence']:.3f})")
            plt.axis('off')
            plt.show()
    else:
        print("No image provided or file not found.")
    
    print("\nDemo complete!")


if __name__ == "__main__":
    main()
