{"model_info": {"input_shape": [null, 299, 299, 3], "output_shape": [null, 1], "total_parameters": 72977, "trainable_parameters": "72977", "non_trainable_parameters": 0, "num_layers": 18, "layer_names": ["conv2d", "activation", "max_pooling2d", "conv2d_1", "activation_1", "max_pooling2d_1", "conv2d_2", "activation_2", "max_pooling2d_2", "conv2d_3", "activation_3", "max_pooling2d_3", "flatten", "dense", "activation_4", "dropout", "dense_1", "activation_5"], "layer_types": ["Conv2D", "Activation", "MaxPooling2D", "Conv2D", "Activation", "MaxPooling2D", "Conv2D", "Activation", "MaxPooling2D", "Conv2D", "Activation", "MaxPooling2D", "<PERSON><PERSON>", "<PERSON><PERSON>", "Activation", "Dropout", "<PERSON><PERSON>", "Activation"]}, "architecture_details": {"model_type": "Convolutional Neural Network", "task": "Binary Classification", "classes": ["Dysplastic", "Normal"], "input_preprocessing": "Normalization to [0,1]", "loss_function": "Binary Crossentropy", "optimizer": "<PERSON>", "activation_output": "<PERSON><PERSON><PERSON><PERSON>"}, "computational_complexity": {"input_size": 268203, "memory_usage_mb": 50.278385162353516, "flops_estimate": 145954}}