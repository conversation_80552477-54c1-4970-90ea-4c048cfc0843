#!/usr/bin/env python3
"""
DysplaciaNet Dataset Visualization Functions

This script provides comprehensive visualization functions for the DysplaciaNet dataset,
including image display, distribution analysis, and prediction visualization.

Usage:
    python dataset_visualizer.py
    
    Or import as module:
    from dataset_visualizer import DatasetVisualizer

Author: Generated for DysplaciaNet Project
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
import pandas as pd
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('default')
sns.set_palette("husl")

try:
    from PIL import Image
    import cv2
    print("✓ Image processing libraries imported successfully")
except ImportError as e:
    print(f"✗ Error importing image libraries: {e}")
    sys.exit(1)

class DatasetVisualizer:
    """
    Comprehensive dataset visualization class for DysplaciaNet.
    """
    
    def __init__(self, figsize=(12, 8)):
        """
        Initialize the visualizer.
        
        Args:
            figsize (tuple): Default figure size for plots
        """
        self.figsize = figsize
        self.colors = {
            'dysplastic': '#FF6B6B',  # Red
            'normal': '#4ECDC4',      # Teal
            'correct': '#51CF66',     # Green
            'incorrect': '#FF8787',   # Light red
            'neutral': '#868E96'      # Gray
        }
        
        print("Dataset Visualizer Initialized")
        print(f"Default figure size: {figsize}")
    
    def visualize_image_grid(self, image_paths, labels=None, predictions=None, 
                           titles=None, grid_size=(3, 3), figsize=None):
        """
        Display a grid of images with optional labels and predictions.
        
        Args:
            image_paths (list): List of image file paths
            labels (list, optional): True labels (0=dysplastic, 1=normal)
            predictions (list, optional): Predicted labels
            titles (list, optional): Custom titles for each image
            grid_size (tuple): Grid dimensions (rows, cols)
            figsize (tuple, optional): Figure size
        """
        if figsize is None:
            figsize = self.figsize
        
        rows, cols = grid_size
        fig, axes = plt.subplots(rows, cols, figsize=figsize)
        
        # Handle single subplot case
        if rows == 1 and cols == 1:
            axes = [axes]
        elif rows == 1 or cols == 1:
            axes = axes.flatten()
        else:
            axes = axes.flatten()
        
        for i in range(rows * cols):
            ax = axes[i]
            
            if i < len(image_paths) and os.path.exists(image_paths[i]):
                try:
                    # Load and display image
                    img = Image.open(image_paths[i])
                    ax.imshow(img)
                    
                    # Create title
                    if titles and i < len(titles):
                        title = titles[i]
                    else:
                        title = os.path.basename(image_paths[i])
                        
                        # Add label information if available
                        if labels and i < len(labels):
                            true_class = "Normal" if labels[i] == 1 else "Dysplastic"
                            title += f"\nTrue: {true_class}"
                        
                        # Add prediction information if available
                        if predictions and i < len(predictions):
                            pred_class = "Normal" if predictions[i] == 1 else "Dysplastic"
                            title += f"\nPred: {pred_class}"
                            
                            # Color code based on correctness
                            if labels and i < len(labels):
                                color = self.colors['correct'] if labels[i] == predictions[i] else self.colors['incorrect']
                                ax.set_title(title, color=color, fontweight='bold', fontsize=10)
                            else:
                                ax.set_title(title, fontsize=10)
                        else:
                            ax.set_title(title, fontsize=10)
                    
                except Exception as e:
                    ax.text(0.5, 0.5, f"Error loading\n{os.path.basename(image_paths[i])}\n{str(e)}", 
                           ha='center', va='center', transform=ax.transAxes, fontsize=8)
                    ax.set_title("Error", color='red')
            else:
                # Empty subplot or file not found
                if i < len(image_paths):
                    ax.text(0.5, 0.5, f"File not found:\n{os.path.basename(image_paths[i])}", 
                           ha='center', va='center', transform=ax.transAxes, fontsize=8)
                    ax.set_title("Not Found", color='red')
                else:
                    ax.axis('off')
            
            ax.axis('off')
        
        plt.tight_layout()
        plt.show()
    
    def plot_class_distribution(self, labels, class_names=None, title="Class Distribution"):
        """
        Plot the distribution of classes in the dataset.
        
        Args:
            labels (list): List of class labels
            class_names (list, optional): Names for the classes
            title (str): Plot title
        """
        if class_names is None:
            class_names = ['Dysplastic', 'Normal']
        
        # Count classes
        unique, counts = np.unique(labels, return_counts=True)
        
        # Create figure
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figsize)
        
        # Bar plot
        colors = [self.colors['dysplastic'], self.colors['normal']]
        bars = ax1.bar([class_names[i] for i in unique], counts, color=colors)
        ax1.set_title(f"{title} - Bar Chart")
        ax1.set_ylabel("Count")
        
        # Add count labels on bars
        for bar, count in zip(bars, counts):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01*max(counts),
                    f'{count}', ha='center', va='bottom', fontweight='bold')
        
        # Pie chart
        ax2.pie(counts, labels=[f"{class_names[i]}\n({counts[j]})" for j, i in enumerate(unique)], 
               colors=colors, autopct='%1.1f%%', startangle=90)
        ax2.set_title(f"{title} - Pie Chart")
        
        plt.tight_layout()
        plt.show()
        
        # Print statistics
        total = sum(counts)
        print(f"\n{title} Statistics:")
        print("-" * 30)
        for i, (class_idx, count) in enumerate(zip(unique, counts)):
            percentage = (count / total) * 100
            print(f"{class_names[class_idx]}: {count} ({percentage:.1f}%)")
        print(f"Total: {total}")
    
    def plot_prediction_analysis(self, true_labels, predictions, probabilities=None):
        """
        Create comprehensive prediction analysis plots.
        
        Args:
            true_labels (list): True class labels
            predictions (list): Predicted class labels  
            probabilities (list, optional): Prediction probabilities
        """
        fig = plt.figure(figsize=(15, 10))
        
        # Confusion Matrix
        ax1 = plt.subplot(2, 3, 1)
        from sklearn.metrics import confusion_matrix
        cm = confusion_matrix(true_labels, predictions)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=['Dysplastic', 'Normal'],
                   yticklabels=['Dysplastic', 'Normal'], ax=ax1)
        ax1.set_title('Confusion Matrix')
        ax1.set_xlabel('Predicted')
        ax1.set_ylabel('True')
        
        # Accuracy by class
        ax2 = plt.subplot(2, 3, 2)
        correct_predictions = np.array(true_labels) == np.array(predictions)
        class_accuracies = []
        class_names = ['Dysplastic', 'Normal']
        
        for class_idx in [0, 1]:
            class_mask = np.array(true_labels) == class_idx
            if np.sum(class_mask) > 0:
                class_acc = np.mean(correct_predictions[class_mask])
                class_accuracies.append(class_acc)
            else:
                class_accuracies.append(0)
        
        colors = [self.colors['dysplastic'], self.colors['normal']]
        bars = ax2.bar(class_names, class_accuracies, color=colors)
        ax2.set_title('Accuracy by Class')
        ax2.set_ylabel('Accuracy')
        ax2.set_ylim(0, 1)
        
        # Add accuracy labels
        for bar, acc in zip(bars, class_accuracies):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # Prediction distribution
        if probabilities is not None:
            ax3 = plt.subplot(2, 3, 3)
            probs = np.array(probabilities)
            labels = np.array(true_labels)
            
            # Histogram for each class
            ax3.hist(probs[labels == 0], alpha=0.7, label='Dysplastic (True)', 
                    bins=20, color=self.colors['dysplastic'])
            ax3.hist(probs[labels == 1], alpha=0.7, label='Normal (True)', 
                    bins=20, color=self.colors['normal'])
            ax3.axvline(x=0.5, color='black', linestyle='--', label='Threshold')
            ax3.set_xlabel('Prediction Probability')
            ax3.set_ylabel('Count')
            ax3.set_title('Probability Distribution')
            ax3.legend()
        
        # Metrics summary
        ax4 = plt.subplot(2, 3, 4)
        ax4.axis('off')
        
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        accuracy = accuracy_score(true_labels, predictions)
        precision = precision_score(true_labels, predictions, average='binary')
        recall = recall_score(true_labels, predictions, average='binary')
        f1 = f1_score(true_labels, predictions, average='binary')
        
        metrics_text = f"""
        Performance Metrics:
        
        Accuracy:  {accuracy:.3f}
        Precision: {precision:.3f}
        Recall:    {recall:.3f}
        F1-Score:  {f1:.3f}
        
        Total Samples: {len(true_labels)}
        Correct: {np.sum(correct_predictions)}
        Incorrect: {len(true_labels) - np.sum(correct_predictions)}
        """
        
        ax4.text(0.1, 0.9, metrics_text, transform=ax4.transAxes, fontsize=12,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        
        # ROC Curve (if probabilities available)
        if probabilities is not None:
            ax5 = plt.subplot(2, 3, 5)
            from sklearn.metrics import roc_curve, auc
            
            fpr, tpr, _ = roc_curve(true_labels, probabilities)
            roc_auc = auc(fpr, tpr)
            
            ax5.plot(fpr, tpr, color=self.colors['normal'], lw=2, 
                    label=f'ROC Curve (AUC = {roc_auc:.3f})')
            ax5.plot([0, 1], [0, 1], color='gray', lw=1, linestyle='--')
            ax5.set_xlim([0.0, 1.0])
            ax5.set_ylim([0.0, 1.05])
            ax5.set_xlabel('False Positive Rate')
            ax5.set_ylabel('True Positive Rate')
            ax5.set_title('ROC Curve')
            ax5.legend(loc="lower right")
            ax5.grid(True, alpha=0.3)
        
        # Sample predictions visualization
        ax6 = plt.subplot(2, 3, 6)
        sample_indices = np.random.choice(len(true_labels), min(20, len(true_labels)), replace=False)
        
        x_pos = range(len(sample_indices))
        true_sample = [true_labels[i] for i in sample_indices]
        pred_sample = [predictions[i] for i in sample_indices]
        
        ax6.scatter(x_pos, true_sample, color=self.colors['normal'], label='True', alpha=0.7, s=50)
        ax6.scatter(x_pos, pred_sample, color=self.colors['dysplastic'], label='Predicted', alpha=0.7, s=30, marker='x')
        ax6.set_xlabel('Sample Index')
        ax6.set_ylabel('Class (0=Dysplastic, 1=Normal)')
        ax6.set_title('Sample Predictions vs True Labels')
        ax6.legend()
        ax6.grid(True, alpha=0.3)
        ax6.set_ylim(-0.1, 1.1)
        
        plt.tight_layout()
        plt.show()
    
    def create_synthetic_dataset_demo(self, num_samples=12):
        """
        Create a demonstration with synthetic images to show visualization capabilities.
        
        Args:
            num_samples (int): Number of synthetic images to create
        """
        print(f"Creating synthetic dataset demo with {num_samples} samples...")
        
        # Generate synthetic images and labels
        synthetic_images = []
        labels = []
        predictions = []
        probabilities = []
        
        np.random.seed(42)  # For reproducibility
        
        for i in range(num_samples):
            # Create synthetic image
            if i % 2 == 0:
                # "Dysplastic" pattern - more chaotic
                img = np.random.rand(100, 100, 3) * 255
                noise = np.random.normal(0, 50, (100, 100, 3))
                img = np.clip(img + noise, 0, 255)
                true_label = 0
                pred_prob = np.random.uniform(0.1, 0.6)  # Lower probability for dysplastic
            else:
                # "Normal" pattern - more structured
                img = np.ones((100, 100, 3)) * 128
                # Add some structure
                img[20:80, 20:80, :] = 200
                img[40:60, 40:60, :] = 100
                noise = np.random.normal(0, 20, (100, 100, 3))
                img = np.clip(img + noise, 0, 255)
                true_label = 1
                pred_prob = np.random.uniform(0.6, 0.9)  # Higher probability for normal
            
            synthetic_images.append(img.astype(np.uint8))
            labels.append(true_label)
            probabilities.append(pred_prob)
            predictions.append(1 if pred_prob > 0.5 else 0)
        
        # Save synthetic images temporarily
        temp_paths = []
        for i, img in enumerate(synthetic_images):
            temp_path = f"temp_synthetic_{i}.png"
            Image.fromarray(img).save(temp_path)
            temp_paths.append(temp_path)
        
        try:
            # Demonstrate visualizations
            print("\n1. Displaying image grid...")
            self.visualize_image_grid(temp_paths[:9], labels[:9], predictions[:9], 
                                    grid_size=(3, 3))
            
            print("\n2. Plotting class distribution...")
            self.plot_class_distribution(labels, title="Synthetic Dataset")
            
            print("\n3. Creating prediction analysis...")
            self.plot_prediction_analysis(labels, predictions, probabilities)
            
        finally:
            # Clean up temporary files
            for path in temp_paths:
                if os.path.exists(path):
                    os.remove(path)
            print("✓ Temporary files cleaned up")


def main():
    """Main function to demonstrate dataset visualization capabilities."""
    print("DysplaciaNet Dataset Visualizer")
    print("=" * 40)
    
    # Initialize visualizer
    visualizer = DatasetVisualizer()
    
    # Run synthetic demo
    print("\nRunning synthetic dataset demonstration...")
    visualizer.create_synthetic_dataset_demo(num_samples=12)
    
    print("\n" + "="*50)
    print("DATASET VISUALIZER DEMO COMPLETE!")
    print("="*50)
    print("Available visualization functions:")
    print("  - visualize_image_grid()")
    print("  - plot_class_distribution()")
    print("  - plot_prediction_analysis()")
    print("  - create_synthetic_dataset_demo()")
    print("\nTo use with real data, import this module:")
    print("  from dataset_visualizer import DatasetVisualizer")


if __name__ == "__main__":
    main()
